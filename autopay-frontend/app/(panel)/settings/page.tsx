'use client'

import PageHeading from '@/app/(panel)/common/components/page-heading'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { BillingAddressForm } from './components/billing-address-form'
import { CompanyNameForm } from './components/company-name-form'
import { CompanySummary } from './components/company-summary'
import { TaxIdForm } from './components/tax-id-form'
import { OrganizationBrandingForm } from './organization/organization-branding-form'

export default function SettingsPage() {
  return (
    <div className="space-y-6">
      <PageHeading
        title="Thiết lập tổ chức"
        description="Quản lý thông tin tổ chức và hồ sơ công ty của bạn"
      />

      <Tabs
        defaultValue="organization"
        className="space-y-6">
        <TabsList>
          <TabsTrigger value="organization">Thông tin tổ chức</TabsTrigger>
          <TabsTrigger value="company"><PERSON><PERSON> sơ công ty</TabsTrigger>
        </TabsList>

        <TabsContent
          value="organization"
          className="space-y-6">
          <div className="space-y-2">
            <h2 className="text-lg font-semibold">Thông tin tổ chức</h2>
            <p className="text-muted-foreground text-sm">
              Quản lý thông tin thương hiệu và branding cho tổ chức của bạn
            </p>
          </div>
          <OrganizationBrandingForm />
        </TabsContent>

        <TabsContent
          value="company"
          className="space-y-6">
          <div className="space-y-2">
            <h2 className="text-lg font-semibold">Hồ sơ công ty</h2>
            <p className="text-muted-foreground text-sm">Cấu hình thông tin cơ bản về tài khoản công ty của bạn</p>
          </div>

          <div className="flex flex-col justify-between gap-4 md:flex-row">
            <div className="space-y-6 md:w-2/3">
              <CompanyNameForm />
              <BillingAddressForm />
              <TaxIdForm />
            </div>
            <Card className="h-fit md:w-1/3">
              <CardHeader>
                <CardTitle>AUTOPAY</CardTitle>
                <CardDescription className="mt-2">CTY TNHH AUTOPAY</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <CompanySummary />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
