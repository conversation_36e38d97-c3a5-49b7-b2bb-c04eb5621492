'use client'

import { But<PERSON> } from '@/components/ui/button'
import { useIsMobile } from '@/hooks/use-mobile'
import { useDomain } from '@/lib/hooks/useDomain'
import { useNavigationRoute } from '@/lib/hooks/useNavigationRoute'
import Image from 'next/image'
import Link from 'next/link'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { BiSupport } from 'react-icons/bi'

import { MdArrowBackIos, MdArrowForwardIos, MdKeyboardArrowRight } from 'react-icons/md'

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion'
import { cn } from '@/lib/utils'
import { useStore } from '@/stores/store'

import Logo from '@/assets/images/logo-black.png'
import { BorderBeam } from '@/components/ui/border-beam'
import { ScrollArea } from '@/components/ui/scroll-area'

import { usePathname } from 'next/navigation'

type ComponentProps = {
  isLeftNavigationMinimized: boolean
}

import Cookies from 'js-cookie'

export default function Component({ isLeftNavigationMinimized }: ComponentProps) {
  // Use isLeftNavigationMinimized on the first render
  // This is to prevent the sidebar from flickering when the page is refreshed
  const [isMinimized, setMinimized] = useState(isLeftNavigationMinimized)

  const { config } = useDomain()

  // Extract branding data from domain config
  const branding = config?.branding || {}
  const brandName = (branding as any)?.name || process.env.NEXT_PUBLIC_APP_NAME || 'AutoPAY'
  const logoUrl = (branding as any)?.logo_url

  useEffect(() => {
    // No depend on isMinimized from server props after the first render
    setMinimized(Cookies.get('isLeftNavigationMinimized') === 'true')
  }, [])

  const pathname = usePathname()
  const [currentPath, setCurrentPath] = useState(pathname)

  const isMobile = useIsMobile()
  const { currentPathClassName, routes, parentPath, isCurrentPath } = useNavigationRoute()

  const { setShowLeftNavigationOnMobile } = useStore()

  // Find the route label that contains the current path
  const currentRouteCategory = routes.find((category) => {
    if (!category.routes) {
      return category.path ? category.path.startsWith(parentPath ?? '') : false
    }

    return category.routes.some((route) => {
      return route.path ? route.path.startsWith(parentPath ?? '') : false
    })
  })

  const allCategoryRouteLabels = useMemo(() => routes.map((category) => category.label), [routes])

  const calculateCategoryRouteLabels = useCallback(() => {
    return isMinimized
      ? allCategoryRouteLabels
      : currentRouteCategory
        ? [currentRouteCategory.label]
        : allCategoryRouteLabels.slice(0, 2)
  }, [isMinimized, allCategoryRouteLabels, currentRouteCategory])

  // Init default active category route labels
  const [activeCategoryRouteLabels, setActiveCategoryRouteLabels] = useState(calculateCategoryRouteLabels())

  useEffect(() => {
    setActiveCategoryRouteLabels(calculateCategoryRouteLabels())
  }, [isMinimized, calculateCategoryRouteLabels])

  // Trigger when clicking on a category route
  const navigationOnValueChange = (value: string[]) => {
    setActiveCategoryRouteLabels(value)
  }

  const toggleNavMinimized = useCallback(() => {
    const newStateOfLeftNavigationMinimized = !(Cookies.get('isLeftNavigationMinimized') === 'true')

    Cookies.set('isLeftNavigationMinimized', String(newStateOfLeftNavigationMinimized))

    const rootLayoutElement = document.querySelector<HTMLElement>('body')
    rootLayoutElement?.style.setProperty('--sidebar-width', newStateOfLeftNavigationMinimized ? '64px' : '248px')

    setMinimized(newStateOfLeftNavigationMinimized)
  }, [])

  useEffect(() => {
    if (isMobile && currentPath !== pathname) {
      setCurrentPath(pathname)
      toggleNavMinimized()
      setShowLeftNavigationOnMobile(false)
    }
  }, [currentPath, isMobile, pathname, toggleNavMinimized, setShowLeftNavigationOnMobile])

  return (
    <>
      <nav className="bg-sidebar z-10 flex h-screen flex-col">
        <div>
          <Link
            href="/"
            className={cn(
              'flex h-14 items-center gap-2 border-b py-4',
              !isMinimized && 'px-4',
              isMinimized && 'justify-center px-2'
            )}>
            <div className="relative overflow-hidden rounded-lg px-2 py-0.5">
              <div>
                {logoUrl ? (
                  <img
                    width="110"
                    src={logoUrl}
                    className="w-auto object-contain"
                    alt={brandName}
                  />
                ) : (
                  <Image
                    width="110"
                    src={Logo}
                    className="w-auto dark:invert-100"
                    alt={brandName}
                  />
                )}
              </div>
              <BorderBeam
                size={25}
                borderWidth={3}
                duration={2}
                delay={10}
                // colorFrom="#0a53f6"
                // colorTo="#ffffff"
              />
            </div>
          </Link>

          <ScrollArea className="h-[85vh]">
            <div className={cn('flex flex-col gap-5', isMinimized ? 'px-2 py-0' : 'p-4')}>
              <Accordion
                type="multiple"
                className="flex flex-col gap-1"
                value={activeCategoryRouteLabels}
                onValueChange={navigationOnValueChange}>
                {routes.map((categoryRoute) => {
                  return (
                    <AccordionItem
                      key={categoryRoute.label}
                      value={categoryRoute.label}
                      className="flex flex-col gap-2 border-0">
                      <AccordionTrigger className={cn('p-1 font-semibold text-[#888E9E]', isMinimized && 'hidden')}>
                        {categoryRoute.label}
                      </AccordionTrigger>
                      {categoryRoute.routes && (
                        <AccordionContent asChild>
                          {categoryRoute.routes.map((route) => {
                            return (
                              <Link
                                key={route.name}
                                href={'/' + route.path}
                                className={currentPathClassName(route, isMinimized && 'justify-center')}
                                data-tooltip-html={categoryRoute.label + ' / ' + route.name}
                                data-tooltip-hidden={!isMinimized}>
                                <div className="flex gap-2">
                                  {React.cloneElement(<route.icon />, {
                                    className: 'size-5',
                                  })}
                                  <span className={cn(isMinimized ? 'hidden' : '')}>{route.name}</span>
                                </div>
                                {!isMinimized && isCurrentPath(route) && (
                                  <MdKeyboardArrowRight className="ml-auto size-4" />
                                )}
                              </Link>
                            )
                          })}
                        </AccordionContent>
                      )}
                    </AccordionItem>
                  )
                })}
              </Accordion>
            </div>
          </ScrollArea>
        </div>

        <div
          className={cn(
            'mt-auto flex items-center gap-4 py-4',
            isMinimized ? 'flex-col justify-center gap-2 px-2' : 'justify-between pl-3'
          )}>
          <Link
            href="/support"
            className="flex items-center gap-2"
            data-tooltip-html="Hỗ trợ"
            data-tooltip-hidden={!isMinimized}>
            <BiSupport className="h-5 w-5" />
            {!isMinimized && <span>Hỗ trợ</span>}
          </Link>
          <Button
            variant="ghost"
            onClick={() => toggleNavMinimized()}
            data-tooltip-html="Mở rộng"
            data-tooltip-hidden={!isMinimized}>
            {!isMinimized ? <MdArrowBackIos className="size-4" /> : <MdArrowForwardIos className="size-4" />}
          </Button>
        </div>
      </nav>
    </>
  )
}
