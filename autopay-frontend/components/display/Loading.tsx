import { Icons } from '@/components/icons'
import { cn } from '@/lib/utils/utils'

export default function Component({
  loadingText,
  className,
  spinnerClassName,
}: {
  loadingText?: string
  className?: string
  spinnerClassName?: string
}) {
  return (
    <div className={cn('text-muted-foreground flex items-center justify-center gap-1', className)}>
      <Icons.spinner className={cn('mr-1 inline-block size-6 animate-spin', spinnerClassName)} />
      {loadingText ?? 'Loading...'}
    </div>
  )
}
